# ===========================================
# Configuration Free Fire Bot Manager
# ===========================================

# Configuration du Bot Telegram
# Obtenez votre token depuis @BotFather sur Telegram
BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz

# Chat ID personnel (obtenez-le en envoyant un message à votre bot puis visitez :
# https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates)
CHAT_ID=123456789

# Chat ID du groupe (négatif, ex: -123456789)
GROUP_CHAT_ID=-123456789

# Configuration Flask
FLASK_SECRET_KEY=your-super-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True
FLASK_HOST=0.0.0.0
FLASK_PORT=5000

# APIs Free Fire
# Clé API pour les services Free Fire
API_KEY=ch9ayfa

# URLs des APIs Free Fire
API_BASE_URL_LIKES=https://likes-ch9ayfa-free.vercel.app
API_BASE_URL_SPAM=https://spam-ch9ayfa.vercel.app
API_BASE_URL_EVENTS=https://eventes-ch9ayfa.vercel.app
API_BASE_URL_INFO=https://info-ch9ayfa.vercel.app
API_BASE_URL_CHECK=https://ch9ayfa-check-1.vercel.app

# Configuration avancée
# Timeout pour les requêtes API (en secondes)
API_TIMEOUT=30

# Nombre maximum de tentatives pour les requêtes
MAX_RETRIES=3

# Délai entre les requêtes (en secondes)
REQUEST_DELAY=1

# ===========================================
# Instructions d'utilisation :
# 1. Copiez ce fichier vers .env
# 2. Remplacez les valeurs par vos vraies données
# 3. Ne partagez jamais votre fichier .env
# ==========================================
