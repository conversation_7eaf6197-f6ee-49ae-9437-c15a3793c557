# Guide d'Installation - Free Fire Bot Manager

## 🚀 Installation Rapide

### Prérequis
- Python 3.7 ou plus récent
- Connexion Internet
- Un bot Telegram (optionnel pour les tests)

### Étapes d'Installation

1. **Télécharger le projet**
   ```bash
   # Si vous avez git
   git clone <repository-url>
   cd parfume
   
   # Ou téléchargez et extrayez le ZIP
   ```

2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

3. **Lancer l'application**
   
   **Option A : Script automatique (Windows)**
   ```bash
   start.bat
   ```
   
   **Option B : Commande manuelle**
   ```bash
   python app.py
   ```
   
   **Option C : Serveur de test**
   ```bash
   python server.py
   ```

4. **Accéder à l'application**
   - Ouvrez votre navigateur
   - Allez à `http://localhost:5000` (Flask) ou `http://localhost:8000` (serveur de test)

## 🔧 Configuration du Bot Telegram

### C<PERSON>er un Bot Telegram

1. **Contactez @BotFather sur Telegram**
   - Envoyez `/newbot`
   - Choisissez un nom pour votre bot
   - Choisissez un nom d'utilisateur (doit finir par "bot")
   - Copiez le token fourni

2. **Obtenir votre Chat ID**
   - Envoyez un message à votre bot
   - Visitez : `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
   - Trouvez votre `chat_id` dans la réponse JSON

3. **Obtenir le Chat ID d'un Groupe**
   - Ajoutez votre bot au groupe
   - Envoyez un message dans le groupe
   - Visitez la même URL que ci-dessus
   - Le chat_id du groupe sera négatif (ex: -123456789)

### Configuration dans l'Application

1. Allez sur le **Dashboard**
2. Remplissez les champs :
   - **Token du Bot** : Le token fourni par BotFather
   - **Chat ID** : Votre chat ID personnel
   - **Chat ID du Groupe** : Chat ID du groupe (optionnel)
3. La configuration est sauvegardée automatiquement

## 🎮 Utilisation des APIs Free Fire

### API Likes
- **URL** : `https://likes-ch9ayfa-free.vercel.app/like?uid={uid}&key=ch9ayfa-l7away`
- **Utilisation** : Entrez l'UID du joueur et cliquez sur "Envoyer Like"

### API Spam
- **URL** : `https://spam-ch9ayfa.vercel.app/spam?id={uid}`
- **Utilisation** : Entrez l'UID du joueur et cliquez sur "Envoyer Spam"

### API Événements
- **URL** : `https://eventes-ch9ayfa.vercel.app/eventes?region={region}&key=ch9ayfa`
- **Utilisation** : Sélectionnez une région et cliquez sur "Obtenir Événements"
- **Régions disponibles** : ME, EU, NA, SA, AS

### API Informations
- **URL** : `https://info-ch9ayfa.vercel.app/{uid}`
- **Utilisation** : Entrez l'UID du joueur et cliquez sur "Obtenir Infos"

### API Vérification de Statut
- **URL** : `https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid={uid}`
- **Utilisation** : Entrez l'UID du joueur et cliquez sur "Vérifier Statut"

## 🛠️ Dépannage

### Problèmes Courants

1. **L'application ne se lance pas**
   ```bash
   # Vérifiez que Python est installé
   python --version
   
   # Vérifiez que Flask est installé
   pip list | grep Flask
   
   # Réinstallez les dépendances
   pip install -r requirements.txt --force-reinstall
   ```

2. **Port déjà utilisé**
   ```bash
   # Changez le port dans app.py
   app.run(debug=True, host='0.0.0.0', port=5001)  # Utilisez 5001 au lieu de 5000
   ```

3. **Erreurs de connexion aux APIs**
   - Vérifiez votre connexion Internet
   - Les APIs externes peuvent être temporairement indisponibles
   - Attendez quelques minutes et réessayez

4. **Bot Telegram ne répond pas**
   - Vérifiez que le token est correct
   - Vérifiez que le chat ID est correct
   - Assurez-vous d'avoir envoyé au moins un message au bot

### Logs et Debug

Pour voir les erreurs détaillées :
```bash
# Lancez avec debug activé
python app.py

# Ou consultez les logs dans le terminal
```

## 📁 Structure du Projet

```
parfume/
├── app.py                 # Application Flask principale
├── simple_app.py          # Version simplifiée
├── server.py              # Serveur de test
├── run.py                 # Script de lancement alternatif
├── requirements.txt       # Dépendances Python
├── start.bat             # Script de démarrage Windows
├── README.md             # Documentation principale
├── INSTALLATION.md       # Ce guide d'installation
├── .env.example          # Exemple de configuration
├── templates/            # Templates HTML
│   ├── base.html         # Template de base
│   ├── index.html        # Page d'accueil
│   └── dashboard.html    # Dashboard principal
└── static/               # Fichiers statiques
    └── style.css         # Styles personnalisés
```

## 🔒 Sécurité

- **Ne partagez jamais** votre token de bot Telegram
- **Gardez privés** vos chat IDs
- **Utilisez** des variables d'environnement pour les données sensibles
- **Limitez** l'accès à votre application (ne l'exposez pas publiquement)

## 📞 Support

Si vous rencontrez des problèmes :
1. Consultez ce guide d'installation
2. Vérifiez les logs d'erreur
3. Testez avec le serveur de test (`python server.py`)
4. Créez une issue avec les détails de l'erreur

## 🎯 Prochaines Étapes

Une fois l'installation terminée :
1. Configurez votre bot Telegram
2. Testez les APIs Free Fire
3. Personnalisez l'interface selon vos besoins
4. Explorez les fonctionnalités avancées
