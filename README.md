# Free Fire Bot Manager

Une application web Flask avec Bootstrap pour gérer un bot Telegram Free Fire avec accès aux APIs Free Fire.

## Fonctionnalités

- 🤖 **Configuration Bot Telegram** : Configurez facilement votre token de bot et chat IDs
- ❤️ **API Likes** : Envoyez des likes Free Fire via l'API
- 📧 **API Spam** : Fonctionnalité de spam avec contrôle
- 📅 **API Événements** : Consultez les événements Free Fire par région
- ℹ️ **API Informations** : Obtenez des informations détaillées sur les joueurs
- ✅ **Vérification de Statut** : Vérifiez le statut des joueurs
- 💬 **Messages Telegram** : Envoyez des messages via votre bot
- 💾 **Sauvegarde Automatique** : Configuration sauvegardée automatiquement

## APIs Utilisées

- `https://likes-ch9ayfa-free.vercel.app/like?uid={uid}&key=ch9ayfa-l7away`
- `https://spam-ch9ayfa.vercel.app/spam?id={uid}`
- `https://eventes-ch9ayfa.vercel.app/eventes?region={region}&key=ch9ayfa`
- `https://info-ch9ayfa.vercel.app/{uid}`
- `https://ch9ayfa-check-1.vercel.app/check_status?key=ch9ayfa&uid={uid}`

## Installation

1. **Cloner le projet**
   ```bash
   git clone <repository-url>
   cd parfume
   ```

2. **Installer les dépendances**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configuration (optionnel)**
   ```bash
   cp .env.example .env
   # Éditez le fichier .env avec vos configurations
   ```

4. **Lancer l'application**
   ```bash
   python app.py
   ```

5. **Accéder à l'application**
   Ouvrez votre navigateur et allez à `http://localhost:5000`

## Utilisation

### Configuration du Bot Telegram

1. Allez sur le **Dashboard**
2. Entrez votre **Token de Bot** Telegram
3. Entrez votre **Chat ID** personnel
4. Entrez le **Chat ID du Groupe** (optionnel)
5. La configuration est sauvegardée automatiquement

### Utilisation des APIs

#### API Likes
- Entrez l'UID du joueur
- Cliquez sur "Envoyer Like"
- Le résultat s'affiche et une notification est envoyée via Telegram

#### API Spam
- Entrez l'UID du joueur
- Cliquez sur "Envoyer Spam"

#### API Événements
- Sélectionnez la région (ME, EU, NA, SA, AS)
- Cliquez sur "Obtenir Événements"

#### API Informations
- Entrez l'UID du joueur
- Cliquez sur "Obtenir Infos"

#### Vérification de Statut
- Entrez l'UID du joueur
- Cliquez sur "Vérifier Statut"

### Envoyer des Messages Telegram

1. Rédigez votre message
2. Choisissez entre "Chat privé" ou "Chat de groupe"
3. Cliquez sur "Envoyer Message"

## Structure du Projet

```
parfume/
├── app.py                 # Application Flask principale
├── requirements.txt       # Dépendances Python
├── .env.example          # Exemple de configuration
├── README.md             # Documentation
├── templates/            # Templates HTML
│   ├── base.html         # Template de base
│   ├── index.html        # Page d'accueil
│   └── dashboard.html    # Dashboard principal
└── static/               # Fichiers statiques (CSS, JS, images)
```

## Technologies Utilisées

- **Backend** : Flask (Python)
- **Frontend** : Bootstrap 5, jQuery
- **APIs** : Requests pour les appels HTTP
- **Stockage** : LocalStorage pour la configuration

## Sécurité

- Ne partagez jamais votre token de bot Telegram
- Gardez vos chat IDs privés
- Utilisez des variables d'environnement pour les données sensibles

## Support

Pour toute question ou problème, veuillez créer une issue dans le repository.

## Licence

Ce projet est sous licence MIT.
