from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
import requests
import json
import os
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# Configuration
API_ENDPOINTS = {
    'like': 'https://likes-ch9ayfa-free.vercel.app/like',
    'spam': 'https://spam-ch9ayfa.vercel.app/spam',
    'events': 'https://eventes-ch9ayfa.vercel.app/eventes',
    'info': 'https://info-ch9ayfa.vercel.app',
    'check_status': 'https://ch9ayfa-check-1.vercel.app/check_status'
}

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/dashboard')
def dashboard():
    return render_template('dashboard.html')

@app.route('/api/like', methods=['POST'])
def api_like():
    try:
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({'error': 'UID is required'}), 400
        
        response = requests.get(f"{API_ENDPOINTS['like']}?uid={uid}&key=ch9ayfa-l7away")
        
        return jsonify({
            'success': True,
            'data': response.json() if response.headers.get('content-type') == 'application/json' else response.text,
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/spam', methods=['POST'])
def api_spam():
    try:
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({'error': 'UID is required'}), 400
        
        response = requests.get(f"{API_ENDPOINTS['spam']}?id={uid}")
        
        return jsonify({
            'success': True,
            'data': response.json() if response.headers.get('content-type') == 'application/json' else response.text,
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/events', methods=['POST'])
def api_events():
    try:
        data = request.get_json()
        region = data.get('region', 'ME')
        
        response = requests.get(f"{API_ENDPOINTS['events']}?region={region}&key=ch9ayfa")
        
        return jsonify({
            'success': True,
            'data': response.json() if response.headers.get('content-type') == 'application/json' else response.text,
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/info', methods=['POST'])
def api_info():
    try:
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({'error': 'UID is required'}), 400
        
        response = requests.get(f"{API_ENDPOINTS['info']}/{uid}")
        
        return jsonify({
            'success': True,
            'data': response.json() if response.headers.get('content-type') == 'application/json' else response.text,
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/check_status', methods=['POST'])
def api_check_status():
    try:
        data = request.get_json()
        uid = data.get('uid')
        
        if not uid:
            return jsonify({'error': 'UID is required'}), 400
        
        response = requests.get(f"{API_ENDPOINTS['check_status']}?key=ch9ayfa&uid={uid}")
        
        return jsonify({
            'success': True,
            'data': response.json() if response.headers.get('content-type') == 'application/json' else response.text,
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/send_telegram', methods=['POST'])
def send_telegram():
    try:
        data = request.get_json()
        bot_token = data.get('bot_token')
        chat_id = data.get('chat_id')
        message = data.get('message')
        
        if not all([bot_token, chat_id, message]):
            return jsonify({'error': 'Bot token, chat ID, and message are required'}), 400
        
        telegram_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            'chat_id': chat_id,
            'text': message,
            'parse_mode': 'HTML'
        }
        
        response = requests.post(telegram_url, json=payload)
        
        return jsonify({
            'success': True,
            'data': response.json(),
            'status_code': response.status_code
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
