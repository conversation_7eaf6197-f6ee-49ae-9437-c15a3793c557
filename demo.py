#!/usr/bin/env python3
"""
Démonstration des APIs Free Fire
Ce script teste toutes les APIs sans interface web
"""

import requests
import json
import time
from datetime import datetime

# Configuration des APIs
API_ENDPOINTS = {
    'like': 'https://likes-ch9ayfa-free.vercel.app/like',
    'spam': 'https://spam-ch9ayfa.vercel.app/spam',
    'events': 'https://eventes-ch9ayfa.vercel.app/eventes',
    'info': 'https://info-ch9ayfa.vercel.app',
    'check_status': 'https://ch9ayfa-check-1.vercel.app/check_status'
}

def print_header(title):
    """Affiche un en-tête formaté"""
    print("\n" + "="*50)
    print(f"  {title}")
    print("="*50)

def print_result(api_name, response, url):
    """Affiche le résultat d'une API"""
    print(f"\n🔗 URL: {url}")
    print(f"📊 Status Code: {response.status_code}")
    
    if response.status_code == 200:
        print("✅ Succès!")
        try:
            data = response.json()
            print(f"📄 Réponse JSON:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
        except:
            print(f"📄 Réponse Texte:")
            print(response.text)
    else:
        print("❌ Erreur!")
        print(f"📄 Réponse: {response.text}")

def test_like_api(uid="8784017287"):
    """Test de l'API Likes"""
    print_header("TEST API LIKES")
    url = f"{API_ENDPOINTS['like']}?uid={uid}&key=ch9ayfa-l7away"
    
    try:
        response = requests.get(url, timeout=10)
        print_result("Likes", response, url)
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")

def test_spam_api(uid="8784017287"):
    """Test de l'API Spam"""
    print_header("TEST API SPAM")
    url = f"{API_ENDPOINTS['spam']}?id={uid}"
    
    try:
        response = requests.get(url, timeout=10)
        print_result("Spam", response, url)
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")

def test_events_api(region="ME"):
    """Test de l'API Événements"""
    print_header("TEST API ÉVÉNEMENTS")
    url = f"{API_ENDPOINTS['events']}?region={region}&key=ch9ayfa"
    
    try:
        response = requests.get(url, timeout=10)
        print_result("Events", response, url)
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")

def test_info_api(uid="2511293320"):
    """Test de l'API Informations"""
    print_header("TEST API INFORMATIONS")
    url = f"{API_ENDPOINTS['info']}/{uid}"
    
    try:
        response = requests.get(url, timeout=10)
        print_result("Info", response, url)
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")

def test_status_api(uid="8784017287"):
    """Test de l'API Vérification de Statut"""
    print_header("TEST API VÉRIFICATION DE STATUT")
    url = f"{API_ENDPOINTS['check_status']}?key=ch9ayfa&uid={uid}"
    
    try:
        response = requests.get(url, timeout=10)
        print_result("Status", response, url)
    except Exception as e:
        print(f"❌ Erreur lors de la requête: {e}")

def main():
    """Fonction principale"""
    print("🚀 DÉMONSTRATION DES APIs FREE FIRE")
    print(f"⏰ Démarré le: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🔧 Test de toutes les APIs disponibles...")
    
    # Test de toutes les APIs
    test_like_api()
    time.sleep(2)  # Délai entre les requêtes
    
    test_spam_api()
    time.sleep(2)
    
    test_events_api()
    time.sleep(2)
    
    test_info_api()
    time.sleep(2)
    
    test_status_api()
    
    print_header("DÉMONSTRATION TERMINÉE")
    print("✅ Tous les tests ont été exécutés")
    print("📝 Consultez les résultats ci-dessus")
    print("🌐 Pour utiliser l'interface web, lancez: python app.py")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Démonstration interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n\n❌ Erreur inattendue: {e}")
    
    input("\n📱 Appuyez sur Entrée pour fermer...")
