#!/usr/bin/env python3
"""
Script de lancement pour Free Fire Bot Manager
"""

from app import app

if __name__ == '__main__':
    print("🚀 Démarrage de Free Fire Bot Manager...")
    print("📱 Application disponible sur: http://localhost:5000")
    print("🔧 Mode debug activé")
    print("-" * 50)
    
    try:
        app.run(
            debug=True,
            host='0.0.0.0',
            port=5000,
            use_reloader=True
        )
    except KeyboardInterrupt:
        print("\n👋 Arrêt de l'application...")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        input("Appuyez sur Entrée pour fermer...")
