#!/usr/bin/env python3
import http.server
import socketserver
import webbrowser
import os
from threading import Timer

PORT = 8000

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.path = '/index.html'
        return http.server.SimpleHTTPRequestHandler.do_GET(self)

def open_browser():
    webbrowser.open(f'http://localhost:{PORT}')

if __name__ == "__main__":
    # Créer un fichier HTML simple pour tester
    html_content = """
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Fire Bot Manager - Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 50px 0;
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.9);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-gamepad fa-4x text-primary mb-4"></i>
                        <h1 class="card-title mb-4">Free Fire Bot Manager</h1>
                        <p class="card-text lead mb-4">
                            Serveur de test fonctionnel ! 🎉
                        </p>
                        <div class="alert alert-success">
                            <h5>✅ Serveur Web Actif</h5>
                            <p>Le serveur fonctionne correctement sur le port 8000.</p>
                        </div>
                        <div class="alert alert-info">
                            <h5>📋 Instructions</h5>
                            <p>Pour utiliser l'application Flask complète :</p>
                            <ol class="text-start">
                                <li>Ouvrez un terminal dans le dossier du projet</li>
                                <li>Exécutez : <code>python app.py</code></li>
                                <li>Accédez à <code>http://localhost:5000</code></li>
                            </ol>
                        </div>
                        <div class="mt-4">
                            <h5>🔗 APIs Free Fire Disponibles :</h5>
                            <ul class="list-unstyled">
                                <li>❤️ Likes API</li>
                                <li>📧 Spam API</li>
                                <li>📅 Events API</li>
                                <li>ℹ️ Info API</li>
                                <li>✅ Status Check API</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
    """
    
    with open('index.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"🚀 Serveur de test démarré sur le port {PORT}")
    print(f"📱 Ouvrez votre navigateur sur: http://localhost:{PORT}")
    print("🔧 Appuyez sur Ctrl+C pour arrêter le serveur")
    print("-" * 50)
    
    # Ouvrir le navigateur après 2 secondes
    Timer(2.0, open_browser).start()
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Serveur arrêté.")
            # Nettoyer le fichier temporaire
            if os.path.exists('index.html'):
                os.remove('index.html')
