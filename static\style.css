/* Styles personnalisés pour Free Fire Bot Manager */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Boutons avec effets */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Cards avec effets */
.card {
    transition: all 0.3s ease;
    position: relative;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

/* Inputs avec focus amélioré */
.form-control:focus {
    transform: scale(1.02);
    transition: all 0.3s ease;
}

/* Résultats avec animation */
.alert {
    animation: fadeIn 0.5s ease-out;
}

/* Spinners personnalisés */
.spinner-border {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navbar avec effet glassmorphism */
.navbar {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Sections API avec couleurs distinctes */
.api-section .card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 15px 15px 0 0 !important;
}

.api-section:nth-child(1) .card-header {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.api-section:nth-child(2) .card-header {
    background: linear-gradient(45deg, #f39c12, #e67e22);
}

.api-section:nth-child(3) .card-header {
    background: linear-gradient(45deg, #27ae60, #229954);
}

.api-section:nth-child(4) .card-header {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
}

/* Loading states */
.loading {
    pointer-events: none;
    opacity: 0.6;
}

.loading .btn {
    cursor: not-allowed;
}

/* Success/Error states */
.alert-success {
    border-left: 4px solid var(--success-color);
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.05));
}

.alert-danger {
    border-left: 4px solid var(--danger-color);
    background: linear-gradient(90deg, rgba(220, 53, 69, 0.1), rgba(220, 53, 69, 0.05));
}

.alert-info {
    border-left: 4px solid var(--info-color);
    background: linear-gradient(90deg, rgba(23, 162, 184, 0.1), rgba(23, 162, 184, 0.05));
}

/* Code blocks */
pre {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    font-size: 0.9rem;
    max-height: 300px;
    overflow-y: auto;
}

/* Scrollbar personnalisé */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, var(--secondary-color), var(--primary-color));
}

/* Tooltips */
.tooltip {
    font-size: 0.875rem;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    padding: 20px 0;
    margin-top: 50px;
}
