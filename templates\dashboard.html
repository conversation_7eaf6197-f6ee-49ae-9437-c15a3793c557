{% extends "base.html" %}

{% block title %}Dashboard - Free Fire Bot Manager{% endblock %}

{% block content %}
<div class="row">
    <!-- Configuration Telegram -->
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-telegram me-2"></i>Configuration Telegram Bot</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="botToken" class="form-label">Token du Bot</label>
                            <input type="text" class="form-control" id="botToken" placeholder="Entrez le token de votre bot">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="chatId" class="form-label">Chat ID</label>
                            <input type="text" class="form-control" id="chatId" placeholder="Entrez le chat ID">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="groupChatId" class="form-label">Chat ID du Groupe</label>
                            <input type="text" class="form-control" id="groupChatId" placeholder="Entrez le chat ID du groupe">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- API Likes -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-heart text-danger me-2"></i>API Likes</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="likeUid" class="form-label">UID du joueur</label>
                    <input type="text" class="form-control" id="likeUid" placeholder="Entrez l'UID">
                </div>
                <button class="btn btn-primary" onclick="callLikeAPI()">
                    <span class="spinner-border spinner-border-sm d-none" id="likeSpinner"></span>
                    <i class="fas fa-heart me-1"></i>Envoyer Like
                </button>
                <div id="likeResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- API Spam -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-envelope text-warning me-2"></i>API Spam</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="spamUid" class="form-label">UID du joueur</label>
                    <input type="text" class="form-control" id="spamUid" placeholder="Entrez l'UID">
                </div>
                <button class="btn btn-warning" onclick="callSpamAPI()">
                    <span class="spinner-border spinner-border-sm d-none" id="spamSpinner"></span>
                    <i class="fas fa-envelope me-1"></i>Envoyer Spam
                </button>
                <div id="spamResult" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- API Événements -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar text-success me-2"></i>API Événements</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="eventRegion" class="form-label">Région</label>
                    <select class="form-control" id="eventRegion">
                        <option value="ME">ME (Middle East)</option>
                        <option value="EU">EU (Europe)</option>
                        <option value="NA">NA (North America)</option>
                        <option value="SA">SA (South America)</option>
                        <option value="AS">AS (Asia)</option>
                    </select>
                </div>
                <button class="btn btn-success" onclick="callEventsAPI()">
                    <span class="spinner-border spinner-border-sm d-none" id="eventsSpinner"></span>
                    <i class="fas fa-calendar me-1"></i>Obtenir Événements
                </button>
                <div id="eventsResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- API Info -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle text-info me-2"></i>API Informations</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="infoUid" class="form-label">UID du joueur</label>
                    <input type="text" class="form-control" id="infoUid" placeholder="Entrez l'UID">
                </div>
                <button class="btn btn-info" onclick="callInfoAPI()">
                    <span class="spinner-border spinner-border-sm d-none" id="infoSpinner"></span>
                    <i class="fas fa-info-circle me-1"></i>Obtenir Infos
                </button>
                <div id="infoResult" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- API Check Status -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-check-circle text-primary me-2"></i>Vérifier Statut</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="statusUid" class="form-label">UID du joueur</label>
                    <input type="text" class="form-control" id="statusUid" placeholder="Entrez l'UID">
                </div>
                <button class="btn btn-primary" onclick="callStatusAPI()">
                    <span class="spinner-border spinner-border-sm d-none" id="statusSpinner"></span>
                    <i class="fas fa-check-circle me-1"></i>Vérifier Statut
                </button>
                <div id="statusResult" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- Envoyer Message Telegram -->
    <div class="col-md-6 mb-4">
        <div class="card api-section">
            <div class="card-header">
                <h5 class="mb-0"><i class="fab fa-telegram text-primary me-2"></i>Envoyer Message</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="telegramMessage" class="form-label">Message</label>
                    <textarea class="form-control" id="telegramMessage" rows="3" placeholder="Entrez votre message"></textarea>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="chatType" id="privatChat" value="private" checked>
                        <label class="form-check-label" for="privatChat">Chat privé</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="chatType" id="groupChat" value="group">
                        <label class="form-check-label" for="groupChat">Chat de groupe</label>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="sendTelegramMessage()">
                    <span class="spinner-border spinner-border-sm d-none" id="telegramSpinner"></span>
                    <i class="fab fa-telegram me-1"></i>Envoyer Message
                </button>
                <div id="telegramResult" class="mt-3"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Fonctions utilitaires
function showSpinner(spinnerId) {
    document.getElementById(spinnerId).classList.remove('d-none');
}

function hideSpinner(spinnerId) {
    document.getElementById(spinnerId).classList.add('d-none');
}

function showResult(resultId, data, isError = false) {
    const resultDiv = document.getElementById(resultId);
    const alertClass = isError ? 'alert-danger' : 'alert-success';

    let content = `<div class="alert ${alertClass}">`;
    if (typeof data === 'object') {
        content += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
    } else {
        content += data;
    }
    content += '</div>';

    resultDiv.innerHTML = content;
}

// API Likes
function callLikeAPI() {
    const uid = document.getElementById('likeUid').value;
    if (!uid) {
        showResult('likeResult', 'Veuillez entrer un UID', true);
        return;
    }

    showSpinner('likeSpinner');

    fetch('/api/like', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid: uid })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('likeSpinner');
        showResult('likeResult', data, !data.success);

        // Envoyer notification Telegram si configuré
        if (data.success) {
            sendNotification(`✅ Like envoyé avec succès pour l'UID: ${uid}`);
        }
    })
    .catch(error => {
        hideSpinner('likeSpinner');
        showResult('likeResult', 'Erreur: ' + error.message, true);
    });
}

// API Spam
function callSpamAPI() {
    const uid = document.getElementById('spamUid').value;
    if (!uid) {
        showResult('spamResult', 'Veuillez entrer un UID', true);
        return;
    }

    showSpinner('spamSpinner');

    fetch('/api/spam', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid: uid })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('spamSpinner');
        showResult('spamResult', data, !data.success);

        if (data.success) {
            sendNotification(`📧 Spam envoyé avec succès pour l'UID: ${uid}`);
        }
    })
    .catch(error => {
        hideSpinner('spamSpinner');
        showResult('spamResult', 'Erreur: ' + error.message, true);
    });
}

// API Événements
function callEventsAPI() {
    const region = document.getElementById('eventRegion').value;

    showSpinner('eventsSpinner');

    fetch('/api/events', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ region: region })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('eventsSpinner');
        showResult('eventsResult', data, !data.success);

        if (data.success) {
            sendNotification(`📅 Événements récupérés pour la région: ${region}`);
        }
    })
    .catch(error => {
        hideSpinner('eventsSpinner');
        showResult('eventsResult', 'Erreur: ' + error.message, true);
    });
}

// API Info
function callInfoAPI() {
    const uid = document.getElementById('infoUid').value;
    if (!uid) {
        showResult('infoResult', 'Veuillez entrer un UID', true);
        return;
    }

    showSpinner('infoSpinner');

    fetch('/api/info', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid: uid })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('infoSpinner');
        showResult('infoResult', data, !data.success);

        if (data.success) {
            sendNotification(`ℹ️ Informations récupérées pour l'UID: ${uid}`);
        }
    })
    .catch(error => {
        hideSpinner('infoSpinner');
        showResult('infoResult', 'Erreur: ' + error.message, true);
    });
}

// API Check Status
function callStatusAPI() {
    const uid = document.getElementById('statusUid').value;
    if (!uid) {
        showResult('statusResult', 'Veuillez entrer un UID', true);
        return;
    }

    showSpinner('statusSpinner');

    fetch('/api/check_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid: uid })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('statusSpinner');
        showResult('statusResult', data, !data.success);

        if (data.success) {
            sendNotification(`✅ Statut vérifié pour l'UID: ${uid}`);
        }
    })
    .catch(error => {
        hideSpinner('statusSpinner');
        showResult('statusResult', 'Erreur: ' + error.message, true);
    });
}

// Envoyer message Telegram
function sendTelegramMessage() {
    const botToken = document.getElementById('botToken').value;
    const message = document.getElementById('telegramMessage').value;
    const chatType = document.querySelector('input[name="chatType"]:checked').value;

    let chatId;
    if (chatType === 'private') {
        chatId = document.getElementById('chatId').value;
    } else {
        chatId = document.getElementById('groupChatId').value;
    }

    if (!botToken || !chatId || !message) {
        showResult('telegramResult', 'Veuillez remplir tous les champs', true);
        return;
    }

    showSpinner('telegramSpinner');

    fetch('/api/send_telegram', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            bot_token: botToken,
            chat_id: chatId,
            message: message
        })
    })
    .then(response => response.json())
    .then(data => {
        hideSpinner('telegramSpinner');
        showResult('telegramResult', data, !data.success);

        if (data.success) {
            document.getElementById('telegramMessage').value = '';
        }
    })
    .catch(error => {
        hideSpinner('telegramSpinner');
        showResult('telegramResult', 'Erreur: ' + error.message, true);
    });
}

// Fonction pour envoyer des notifications automatiques
function sendNotification(message) {
    const botToken = document.getElementById('botToken').value;
    const chatId = document.getElementById('chatId').value;

    if (botToken && chatId) {
        fetch('/api/send_telegram', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                bot_token: botToken,
                chat_id: chatId,
                message: message
            })
        });
    }
}

// Sauvegarder la configuration dans le localStorage
function saveConfig() {
    const config = {
        botToken: document.getElementById('botToken').value,
        chatId: document.getElementById('chatId').value,
        groupChatId: document.getElementById('groupChatId').value
    };
    localStorage.setItem('ffBotConfig', JSON.stringify(config));
}

// Charger la configuration depuis le localStorage
function loadConfig() {
    const config = localStorage.getItem('ffBotConfig');
    if (config) {
        const parsedConfig = JSON.parse(config);
        document.getElementById('botToken').value = parsedConfig.botToken || '';
        document.getElementById('chatId').value = parsedConfig.chatId || '';
        document.getElementById('groupChatId').value = parsedConfig.groupChatId || '';
    }
}

// Sauvegarder automatiquement la configuration
document.getElementById('botToken').addEventListener('input', saveConfig);
document.getElementById('chatId').addEventListener('input', saveConfig);
document.getElementById('groupChatId').addEventListener('input', saveConfig);

// Charger la configuration au chargement de la page
document.addEventListener('DOMContentLoaded', loadConfig);
</script>
{% endblock %}
